name: .NET Core CD

on:
  push:
    branches: [ development ]
  pull_request:
    branches: [ development ]

jobs:
  build:
    name: Build
    runs-on: self-hosted

    steps:
        - name: Checkout code   
          uses: actions/checkout@v3

        - name: Build and test with Docker Compose
          run: |
            docker-compose -f docker-compose.dev.yml build
            docker-compose -f docker-compose.dev.yml up --abort-on-container-exit --exit-code-from kantoku-service


