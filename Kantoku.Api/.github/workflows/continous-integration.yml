name: .NET Core CI

on:
  push:
    branches: [ development ]
  pull_request:
    branches: [ development ]

jobs:
  build:
    name: Build
    runs-on: self-hosted

    steps:
        - name: Checkout code   
          uses: actions/checkout@v3

        - name: Build Docker image
          run: |
            docker build -t kantoku-service-dev .

        - name: Log in to Docker Hub
          uses: docker/login-action@v1
          with:
            username: ${{ secrets.DOCKERHUB_TOKEN }}
            password: ${{ secrets.DOCKERHUB_USERNAME }}

        - name: Push image to Docker Hub
          run: |
            docker tag kantoku-service-dev:latest ${{ secrets.DOCKERHUB_TOKEN }}/kantoku-service-dev:latest
            docker push ${{ secrets.DOCKERHUB_USERNAME }}/kantoku-service-dev:latest
