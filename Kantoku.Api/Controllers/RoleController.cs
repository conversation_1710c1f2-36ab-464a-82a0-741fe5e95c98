using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Dtos.Role.Request;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Role.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class RoleController : BaseController
{
    private readonly IRoleService roleService;
    private readonly IAuditLogService auditLogService;

    public RoleController(
        ITResponseFactory responseFactory,
        IRoleService roleService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.roleService = roleService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get all roles with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for roles</param>
    /// <returns>Filtered list of roles</returns>
    [HttpGet]
    public async Task<GeneralResponse<RolesResponseDto>> GetAll([FromQuery] RoleFilter filter)
    {
        try
        {
            var result = await roleService.GetRolesByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RolesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get role details by ID
    /// </summary>
    /// <param name="id">Role ID to retrieve</param>
    /// <returns>Role details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<RoleResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            var result = await roleService.GetRoleById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RoleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new role
    /// </summary>
    /// <param name="dto">Role creation data</param>
    /// <returns>Created role details</returns>
    [HttpPost]
    public async Task<GeneralResponse<RoleResponseDto>> CreateRole([FromBody] CreateRoleRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RoleResponseDto>();

            var result = await roleService.CreateRole(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RoleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing role
    /// </summary>
    /// <param name="id">Role ID to update</param>
    /// <param name="dto">Updated role data</param>
    /// <returns>Updated role details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<RoleResponseDto>> UpdateRole([FromRoute] Guid id, [FromBody] UpdateRoleRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RoleResponseDto>();

            var result = await roleService.UpdateRole(id, dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RoleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a role
    /// </summary>
    /// <param name="id">Role ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteRole([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await roleService.DeleteRole(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a role
    /// </summary>
    /// <param name="id">Role ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetRoleLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Role>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
