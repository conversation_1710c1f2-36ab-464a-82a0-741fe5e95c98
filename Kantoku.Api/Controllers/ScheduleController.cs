using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ScheduleController : BaseController
{
    private readonly IProjectScheduleService projectScheduleService;
    private readonly IAuditLogService auditLogService;

    public ScheduleController(
        ITResponseFactory responseFactory,
        IProjectScheduleService projectScheduleService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.projectScheduleService = projectScheduleService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Export project schedules to PDF format
    /// </summary>
    /// <param name="fromDate">Start date in yyyy-MM-dd format</param>
    /// <param name="toDate">End date in yyyy-MM-dd format</param>
    /// <returns>PDF file as byte array</returns>
    [HttpGet("export")]
    public async Task<GeneralResponse<byte[]>> ExportProjectSchedules([FromQuery] string fromDate, [FromQuery] string toDate)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<byte[]>();

            if (DateOnly.TryParse(fromDate, out DateOnly dateFrom) && DateOnly.TryParse(toDate, out DateOnly dateTo))
            {
                var pdfBytes = await projectScheduleService.ExportProjectSchedules(dateFrom, dateTo);
                return Success(pdfBytes);
            }
            else
            {
                return BadRequest<byte[]>();
            }
        }
        catch (BusinessException e)
        {
            return Fail<byte[]>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get project schedules for currently in-progress projects
    /// </summary>
    /// <param name="filter">Filter parameters for project schedules</param>
    /// <returns>Paginated list of project schedules</returns>
    [HttpGet]
    public async Task<GeneralResponse<ProjectSchedulesResponseDto>> GetProjectSchedules([FromQuery] ProjectScheduleFilter filter)

    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectSchedulesResponseDto>();

            var res = await projectScheduleService.GetProjectScheduleByFilter(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectSchedulesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get project schedules for currently in-progress projects
    /// </summary>
    /// <param name="id">Project id</param>
    /// <param name="filter">Filter parameters for project schedules</param>
    /// <returns>Paginated list of project schedules</returns>
    [HttpGet("project/{id}")]
    public async Task<GeneralResponse<ProjectScheduleResponsesDto>> GetProjectSchedulesByProjectId([FromRoute] Guid id, [FromQuery] ProjectScheduleFilter filter)

    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectScheduleResponsesDto>();

            var res = await projectScheduleService.GetProjectScheduleByProjectIdAndDate(id, filter.FromDate, filter.ToDate);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponsesDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get project schedule details by id
    /// </summary>
    /// <param name="id">Project schedule id</param>
    /// <returns>Project schedule details</returns> 
    [HttpGet("{id}")]
    public async Task<GeneralResponse<ProjectScheduleResponseDto>> GetProjectSchedule([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectScheduleResponseDto>();

            var res = await projectScheduleService.GetProjectScheduleById(id);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new project schedule
    /// </summary>
    /// <param name="dto">Project schedule creation data</param>
    /// <returns>Created project schedule details</returns>
    [HttpPost]
    public async Task<GeneralResponse<ProjectScheduleResponseDto>> CreateProjectSchedule([FromBody] CreateProjectScheduleRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectScheduleResponseDto>();

            var res = await projectScheduleService.CreateProjectSchedule(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing project schedule's workload
    /// </summary>
    /// <param name="id">Schedule ID to update</param>
    /// <param name="dto">Updated workload information</param>
    /// <returns>Updated project schedule details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<ProjectScheduleResponseDto>> UpdateProjectSchedule([FromRoute] Guid id, [FromBody] UpdateProjectScheduleRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ProjectScheduleResponseDto>();

            var res = await projectScheduleService.UpdateProjectSchedule(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate a single project schedule to multiple projects within a date range
    /// </summary>
    /// <param name="id">Source schedule ID to duplicate</param>
    /// <param name="dto">Duplication parameters including target dates</param>
    /// <returns>List of created project schedules</returns>
    [HttpPost("{id}/duplicate")]
    public async Task<GeneralResponse<ProjectScheduleResponsesDto>> DuplicateProjectSchedule([FromRoute] Guid id, [FromBody] DuplicateProjectScheduleRequestDto dto)
    {
        try
        {
            var res = await projectScheduleService.DuplicateProjectSchedule(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponsesDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate multiple project schedules to multiple projects within a date range
    /// </summary>
    /// <param name="dto">Duplication parameters including source schedules and target dates</param>
    /// <returns>List of created project schedules</returns>
    [HttpPost("duplicates")]
    public async Task<GeneralResponse<ProjectScheduleResponsesDto>> DuplicateProjectSchedules([FromBody] DuplicateProjectSchedulesRequestDto dto)
    {
        try
        {
            var res = await projectScheduleService.DuplicateProjectSchedules(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectScheduleResponsesDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a project schedule and its associated shifts
    /// </summary>
    /// <param name="id">Schedule ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteProjectSchedule([FromRoute] Guid id)
    {
        try
        {
            await projectScheduleService.DeleteProjectSchedule(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new internal employee shift for an existing schedule
    /// </summary>
    /// <param name="id">Schedule ID to add shift to</param>
    /// <param name="dto">Shift creation data</param>
    /// <returns>Created shift details</returns>
    [HttpPost("{id}/shift")]
    public async Task<GeneralResponse<ScheduledEmployeeShiftResponseDto>> CreateScheduledEmployeeShift([FromRoute] Guid id, [FromBody] CreateScheduledEmployeeShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledEmployeeShiftResponseDto>();

            var res = await projectScheduleService.CreateScheduledEmployeeShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledEmployeeShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update a scheduled employee shift's details
    /// </summary>
    /// <param name="id">Shift ID to update</param>
    /// <param name="dto">Updated shift data</param>
    /// <returns>Updated shift details</returns>
    [HttpPut("shift/{id}")]
    public async Task<GeneralResponse<ScheduledEmployeeShiftResponseDto>> UpdateScheduledEmployeeShift([FromRoute] Guid id, [FromBody] UpdateScheduledEmployeeShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledEmployeeShiftResponseDto>();

            var res = await projectScheduleService.UpdateScheduledEmployeeShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledEmployeeShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate a single internal employee shift to a specific project and date
    /// </summary>
    /// <param name="id">Source shift ID to duplicate</param>
    /// <param name="dto">Duplication parameters</param>
    /// <returns>Created shift details</returns>
    [HttpPost("shift/{id}/duplicate")]
    public async Task<GeneralResponse<ScheduledEmployeeShiftResponseDto>> DuplicateScheduledEmployeeShift([FromRoute] Guid id, [FromBody] DuplicateScheduledEmployeeShiftRequestDto dto)
    {
        try
        {
            var res = await projectScheduleService.DuplicateScheduledEmployeeShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledEmployeeShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate multiple internal employee shifts to a project within a date range
    /// </summary>
    /// <param name="id">Source shift ID to duplicate</param>
    /// <param name="dto">Duplication parameters including target dates</param>
    /// <returns>List of created shifts</returns>
    [HttpPost("shift/{id}/duplicates")]
    public async Task<GeneralResponse<ScheduledEmployeeShiftResponsesDto>> DuplicateScheduledEmployeeShifts([FromRoute] Guid id, [FromBody] DuplicateScheduledEmployeeShiftsRequestDto dto)
    {
        try
        {
            var res = await projectScheduleService.DuplicateScheduledEmployeeShifts(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledEmployeeShiftResponsesDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a scheduled internal employee shift
    /// </summary>
    /// <param name="id">Shift ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("shift/{id}")]
    public async Task<GeneralResponse<bool>> DeleteScheduledEmployeeShift([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await projectScheduleService.DeleteScheduledEmployeeShift(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new outsource shift for an existing schedule
    /// </summary>
    /// <param name="id">Schedule ID to add shift to</param>
    /// <param name="dto">Shift creation data</param>
    /// <returns>Created shift details</returns>
    [HttpPost("{id}/outsource")]
    public async Task<GeneralResponse<ScheduledOutSourceShiftResponseDto>> CreateScheduledOutSourceShift([FromRoute] Guid id, [FromBody] CreateScheduledOutSourceShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledOutSourceShiftResponseDto>();

            var res = await projectScheduleService.CreateScheduledOutSourceShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledOutSourceShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update a scheduled outsource shift's details
    /// </summary>
    /// <param name="id">Shift ID to update</param>
    /// <param name="dto">Updated shift data</param>
    /// <returns>Updated shift details</returns>
    [HttpPut("outsource/{id}")]
    public async Task<GeneralResponse<ScheduledOutSourceShiftResponseDto>> UpdateScheduledOutSourceShift([FromRoute] Guid id, [FromBody] UpdateScheduledOutSourceShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledOutSourceShiftResponseDto>();

            var res = await projectScheduleService.UpdateScheduledOutSourceShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledOutSourceShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate a single outsource shift to a specific project and date
    /// </summary>
    /// <param name="id">Source shift ID to duplicate</param>
    /// <param name="dto">Duplication parameters</param>
    /// <returns>Created shift details</returns>
    [HttpPost("outsource/{id}/duplicate")]
    public async Task<GeneralResponse<ScheduledOutSourceShiftResponseDto>> DuplicateScheduledOutSourceShift([FromRoute] Guid id, [FromBody] DuplicateScheduledOutSourceShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledOutSourceShiftResponseDto>();

            var res = await projectScheduleService.DuplicateScheduledOutSourceShift(id, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledOutSourceShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Duplicate multiple outsource shifts to a project within a date range
    /// </summary>
    /// <param name="id">Source shift ID to duplicate</param>
    /// <param name="dto">Duplication parameters including target dates</param>
    /// <returns>List of created shifts</returns>
    [HttpPost("outsource/{id}/duplicates")]
    public async Task<GeneralResponse<ScheduledOutSourceShiftResponsesDto>> DuplicateScheduledOutSourceShifts([FromRoute] Guid id, [FromBody] DuplicateScheduledOutSourceShiftsRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ScheduledOutSourceShiftResponsesDto>();

            var res = await projectScheduleService.DuplicateScheduledOutSourceShifts(id, dto);

            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ScheduledOutSourceShiftResponsesDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a scheduled outsource shift
    /// </summary>
    /// <param name="id">Shift ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("outsource/{id}")]
    public async Task<GeneralResponse<bool>> DeleteScheduledOutSourceShift([FromRoute] Guid id)

    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await projectScheduleService.DeleteScheduledOutSourceShift(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a project schedule
    /// </summary>
    /// <param name="id">Schedule ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetProjectScheduleLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<ProjectSchedule>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
