using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Structure.Request;
using Kantoku.Api.Dtos.Structure.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class StructureController : BaseController
{
    private readonly IStructureService structureService;
    private readonly IAuditLogService auditLogService;
    public StructureController(
        ITResponseFactory responseFactory,
        IStructureService structureService,
        IAuditLogService auditLogService)
        : base(responseFactory)
    {
        this.structureService = structureService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get all structures with optional filtering
    /// </summary>
    /// <param name="filter">Filter parameters for structures</param>
    /// <returns>List of structures matching the filter criteria</returns>
    [HttpGet]
    public async Task<GeneralResponse<StructuresResponseDto>> GetAll([FromQuery] StructureFilter filter)
    {
        try
        {
            var result = await structureService.GetAllStructure(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StructuresResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get a specific structure by its ID
    /// </summary>
    /// <param name="id">Structure ID</param>
    /// <returns>Structure details if found</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<StructureResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<StructureResponseDto>();

            var result = await structureService.GetStructureById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StructureResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get simplified structure information with optional pagination
    /// </summary>
    /// <param name="pageNum">Page number for pagination</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <returns>Simplified structure information</returns>
    [HttpGet("simple-structure-info")]
    public async Task<GeneralResponse<SimpleStructureResponseDto>> GetSimpleStructureInfo([FromQuery] int? pageNum, [FromQuery] int? pageSize)
    {
        try
        {
            var result = await structureService.GetSimpleStructureInfo(pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleStructureResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new structure
    /// </summary>
    /// <param name="requestDto">Structure creation data</param>
    /// <returns>Created structure details</returns>
    [HttpPost]
    public async Task<GeneralResponse<StructureResponseDto>> CreateStructure([FromBody] CreateStructureRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<StructureResponseDto>();

            var result = await structureService.CreateStructure(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StructureResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing structure
    /// </summary>
    /// <param name="id">Structure ID to update</param>
    /// <param name="requestDto">Updated structure data</param>
    /// <returns>Updated structure details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<StructureResponseDto>> UpdateStructure([FromRoute] Guid id, [FromBody] UpdateStructureRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<StructureResponseDto>();

            var result = await structureService.UpdateStructure(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StructureResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a structure
    /// </summary>
    /// <param name="id">Structure ID to delete</param>
    /// <returns>True if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteStructure([FromRoute] Guid id)
    {
        try
        {
            var result = await structureService.DeleteStructure(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific structure
    /// </summary>
    /// <param name="id">Structure ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Audit log entries for the specified structure</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetStructureLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Structure>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
