using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Databases.Repositories;

public interface IRoleRepository
{
    Task<Role?> GetById(Guid roleId, RoleQueryableOptions options);
    Task<(IEnumerable<Role>, int)> GetByFilter(RoleFilter filter, RoleQueryableOptions options);
    Task<IEnumerable<Role>> GetIndependentRoles();
    Task<IEnumerable<Guid>> GetRoleIdsByEmployeeId(Guid employeeId);
    Task<Role?> Create(Role role, RoleQueryableOptions options);
    Task<Role?> Update(Role role, RoleQueryableOptions options);
}

[Repository(ServiceLifetime.Scoped)]
public class RoleRepository : BaseRepository<Role>, IRoleRepository
{
    private readonly IRoleQueryable roleQueryable;

    public RoleRepository(PostgreDbContext context, IRoleQueryable roleQueryable, Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
    : base(context, logger, httpContextAccessor)
    {
        this.roleQueryable = roleQueryable;
    }

    public async Task<(IEnumerable<Role>, int)> GetByFilter(RoleFilter filter, RoleQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var isOrgSuperUser = await IsOrgSuperUser();
            var query = roleQueryable.GetRolesQueryByFilter(filter, options);
            if (!isOrgSuperUser)
            {
                query = query.Where(r => r.IsHidden == false);
            }

            var result = await query
                .OrderBy(r => r.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all roles");
            return (new List<Role>(), 0);
        }
    }

    public async Task<IEnumerable<Role>> GetRolePrivileges(RoleQueryableOptions options)
    {
        try
        {
            var query = roleQueryable.GetRolesQueryIncluded(options)
                .Where(r => r.IsHidden == false);
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all role privileges");
            return [];
        }
    }

    public async Task<IEnumerable<Role>> GetIndependentRoles()
    {
        try
        {
            var hiddenRoles = await roleQueryable.GetRolesQuery(new RoleQueryableOptions())
                .Where(r => r.OrgUid == GetCurrentOrgUid())
                .Where(r => r.StructureUid == null)
                .ToListAsync();
            return hiddenRoles;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all independent roles");
            return [];
        }
    }

    public async Task<Role?> GetById(Guid roleId, RoleQueryableOptions options)
    {
        try
        {
            var query = roleQueryable.GetRolesQueryIncluded(options)
                .Where(r => r.RoleUid == roleId);

            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting role with id {RoleId}", roleId);
            return null;
        }
    }

    public async Task<IEnumerable<Guid>> GetRoleIdsByEmployeeId(Guid employeeId)
    {
        try
        {
            var query = await context.EmployeeRoles
                .AsNoTracking()
                .Where(er => er.EmployeeUid == employeeId)
                .Select(er => er.RoleUid)
                .ToListAsync();
            return query;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting roles by employee id {EmployeeId}", employeeId);
            return [];
        }
    }
    public async Task<Role?> Create(Role role, RoleQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Roles.AddAsync(role);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(role.RoleUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating role {RoleName}", role.RoleName);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Role?> Update(Role role, RoleQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Roles.Update(role);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(role.RoleUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating role {RoleName}", role.RoleName);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
