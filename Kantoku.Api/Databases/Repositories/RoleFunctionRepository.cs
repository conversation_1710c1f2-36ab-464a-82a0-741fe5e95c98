using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using System.Runtime.Intrinsics.Arm;

namespace Kantoku.Api.Databases.Repositories;

public interface IRoleFunctionRepository
{
    Task<IEnumerable<RoleFunction>> GetByEmployeeId(Guid employeeId, RoleFunctionQueryableOptions options);
    Task<IEnumerable<RoleFunction>> GetByRoleId(Guid roleId, RoleFunctionQueryableOptions options);
    Task<IEnumerable<RoleFunction>> GetByRoleIds(IEnumerable<Guid> roleIds, RoleFunctionQueryableOptions options);
    Task<IEnumerable<RoleFunction>> GetAllRoleFunctions(RoleFunctionQueryableOptions options);
    Task<RoleFunction?> GetByFunctionIdAndRoleId(Guid functionId, Guid roleId, RoleFunctionQueryableOptions options);
    Task<IEnumerable<RoleFunction>> CreateMultiple(IEnumerable<RoleFunction> roleFunctions);
    Task<RoleFunction?> Update(RoleFunction roleFunction);
    Task<IEnumerable<RoleFunction>> UpdateMultiple(IEnumerable<RoleFunction> roleFunctions);
}

[Repository(ServiceLifetime.Scoped)]
public class RoleFunctionRepository : BaseRepository<RoleFunction>, IRoleFunctionRepository
{
    private readonly IRoleFunctionQueryable roleFunctionQueryable;
    public RoleFunctionRepository(
        PostgreDbContext context,
        IRoleFunctionQueryable roleFunctionQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
    : base(context, logger, httpContextAccessor)
    {
        this.roleFunctionQueryable = roleFunctionQueryable;
    }

    public async Task<IEnumerable<RoleFunction>> GetAllRoleFunctions(RoleFunctionQueryableOptions options)
    {
        try
        {
            var orgId = GetCurrentOrgUid();
            var query = roleFunctionQueryable.GetRoleFunctionsQueryIncluded(options)
                .Where(rf => rf.Role.OrgUid == orgId)
                .Where(urf => urf.Function.OnlySuperUser == false);
            return await query
                .OrderBy(urf => urf.Function.DisplayOrder)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to get all user role functions");
            return [];
        }
    }

    public async Task<IEnumerable<RoleFunction>> GetByEmployeeId(Guid employeeId, RoleFunctionQueryableOptions options)
    {
        try
        {
            var employeeRoles = await context.EmployeeRoles
                .AsNoTracking()
                .Where(er => er.EmployeeUid == employeeId)
                .Select(er => er.RoleUid)
                .ToListAsync();
            var query = roleFunctionQueryable.GetRoleFunctionsQueryIncluded(options)
                .Where(rf => employeeRoles.Any(er => er == rf.RoleUid));

            var uf = await query
                .ToListAsync();

            if (await IsOrgSuperUser() || await IsSuperUser())
            {
                uf.ForEach(rf => rf.CanRead = true);
            }
            return uf;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to get functions by employee ID {EmployeeId}", employeeId);
            return [];
        }
    }

    public async Task<RoleFunction?> GetByFunctionIdAndRoleId(Guid functionId, Guid roleId, RoleFunctionQueryableOptions options)
    {
        try
        {
            var query = roleFunctionQueryable.GetRoleFunctionsQueryIncluded(options)
                .Where(rf => rf.FunctionUid == functionId && rf.RoleUid == roleId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to get user role function by function ID {FunctionId}, role ID {RoleId}", functionId, roleId);
            return null;
        }
    }

    public async Task<IEnumerable<RoleFunction>> GetByRoleId(Guid roleId, RoleFunctionQueryableOptions options)
    {
        try
        {
            var query = roleFunctionQueryable.GetRoleFunctionsQueryIncluded(options)
                .Where(rf => rf.RoleUid == roleId);
            return await query
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to get user role functions by role ID {RoleId}", roleId);
            return [];
        }
    }

    public async Task<IEnumerable<RoleFunction>> GetByRoleIds(IEnumerable<Guid> roleIds, RoleFunctionQueryableOptions options)
    {
        try
        {
            if (roleIds is null || !roleIds.Any())
            {
                logger.Error("Role IDs are null or empty");
                return [];
            }
            var query = roleFunctionQueryable.GetRoleFunctionsQueryIncluded(options)
                .Where(rf => roleIds.Contains(rf.RoleUid));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to get user role functions by role IDs {RoleIds}", roleIds);
            return [];
        }
    }

    public async Task<IEnumerable<RoleFunction>> CreateMultiple(IEnumerable<RoleFunction> roleFunctions)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.RoleFunctions.AddRangeAsync(roleFunctions);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return roleFunctions;
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to create user role functions");
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<RoleFunction?> Update(RoleFunction roleFunction)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.RoleFunctions.Update(roleFunction);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return roleFunction;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to update user role function with function ID {FunctionId} and role ID {RoleId}",
                roleFunction.FunctionUid, roleFunction.RoleUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<RoleFunction>> UpdateMultiple(IEnumerable<RoleFunction> roleFunctions)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.RoleFunctions.UpdateRange(roleFunctions);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return roleFunctions;
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to update user role functions");
            await transaction.RollbackAsync();
            return [];
        }
    }
}
