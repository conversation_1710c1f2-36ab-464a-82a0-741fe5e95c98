using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class AuditLogQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedAccount { get; set; } = true;
}

public interface IAuditLogQueryable
{
    IQueryable<AuditLog> GetAuditLogQuery(
        AuditLogQueryableOptions options
    );
    IQueryable<AuditLog> GetAuditLogQueryIncluded(
        AuditLogQueryableOptions options,
        IQueryable<AuditLog>? query = null
    );

    IQueryable<AuditLog> GetAuditLogQueryFiltered(
        AuditLogFilter filter,
        AuditLogQueryableOptions options,
        IQueryable<AuditLog>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class AuditLogQueryable(PostgreDbContext context) :
    BaseQueryable<AuditLog>(context), IAuditLogQueryable
{
    public IQueryable<AuditLog> GetAuditLogQuery(
        AuditLogQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<AuditLog> GetAuditLogQueryIncluded(
        AuditLogQueryableOptions options,
        IQueryable<AuditLog>? query = null
    )
    {
        query ??= GetAuditLogQuery(options);

        if (options.IncludedAccount || options.IncludedAll)
        {
            query = query.Include(s => s.Account)
                .ThenInclude(s => s != null ? s.UserInfo : null);
        }

        return query;
    }

    public IQueryable<AuditLog> GetAuditLogQueryFiltered(
        AuditLogFilter filter,
        AuditLogQueryableOptions options,
        IQueryable<AuditLog>? query = null
    )
    {
        query ??= GetAuditLogQueryIncluded(options);

        if (DateOnly.TryParse(filter.DateFrom, out var dateFromObj))
        {
            var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
            var utcTimeFrom = dateFromObj.ToDateTime(TimeOnly.MinValue).Kind == DateTimeKind.Utc
                ? dateFromObj.ToDateTime(TimeOnly.MinValue)
                : TimeZoneInfo.ConvertTimeToUtc(dateFromObj.ToDateTime(TimeOnly.MinValue), jstTimeZone);
            query = query.Where(p => p.Timestamp >= utcTimeFrom);
        }
        if (DateOnly.TryParse(filter.DateTo, out var dateToObj))
        {
            var jstTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
            var utcTimeTo = dateToObj.ToDateTime(TimeOnly.MaxValue).Kind == DateTimeKind.Utc
                ? dateToObj.ToDateTime(TimeOnly.MaxValue)
                : TimeZoneInfo.ConvertTimeToUtc(dateToObj.ToDateTime(TimeOnly.MaxValue), jstTimeZone);
            query = query.Where(p => p.Timestamp <= utcTimeTo);
        }
        if (filter.Action is not null)
        {
            query = query.Where(p => p.Action.ToUpper().Equals(filter.Action.ToUpper()));
        }
        query = query.OrderByDescending(p => p.Timestamp);
        return query;
    }
}
