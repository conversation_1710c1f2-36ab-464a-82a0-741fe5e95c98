using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class AccountQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedUserInfo { get; set; } = false;
    public bool IncludedEmployees { get; set; } = false;
    public bool IncludedAuditLogs { get; set; } = false;
}

public interface IAccountQueryable
{
    IQueryable<Account> GetAccountQuery(
        AccountQueryableOptions options
    );

    IQueryable<Account> GetAccountQueryIncluded(
        AccountQueryableOptions options,
        IQueryable<Account>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class AccountQueryable(PostgreDbContext context) :
    BaseQueryable<Account>(context), IAccountQueryable
{
    public IQueryable<Account> GetAccountQuery(
        AccountQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Account> GetAccountQueryIncluded(
        AccountQueryableOptions options,
        IQueryable<Account>? query = null
    )
    {
        query ??= GetAccountQuery(options);

        if (options.IncludedUserInfo || options.IncludedAll)
        {
            query = query.Include(s => s.UserInfo);
        }
        if (options.IncludedEmployees || options.IncludedAll)
        {
            query = query.Include(s => s.Employees);
        }
        if (options.IncludedAuditLogs || options.IncludedAll)
        {
            query = query.Include(s => s.AuditLogs);
        }

        return query;
    }
}
