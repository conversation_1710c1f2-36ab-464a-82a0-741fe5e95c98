using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Databases.Contexts;

namespace Kantoku.Api.Databases.Queryables;

public abstract class BaseQueryable<TEntity> where TEntity : class
{
    protected readonly PostgreDbContext context;

    protected BaseQueryable(PostgreDbContext context)
    {
        this.context = context;
    }

    protected virtual IQueryable<TEntity> GetBaseQuery()
    {
        return context.Set<TEntity>().AsQueryable();
    }

    protected virtual IQueryable<TEntity> GetQuery(EntityQueryableOptions options)
    {
        var query = GetBaseQuery();
        
        var props = options.GetType().GetProperties();
        int navigationCount = props.Count(prop =>
            prop.PropertyType == typeof(bool) &&
            (bool)(prop.GetValue(options) ?? false));

        if (navigationCount > 1)
        {
            options.IsSplitQuery = true;
        }
        query = ApplyTracking(query, options.IsTracking);
        query = ApplySplitQuery(query, options.IsSplitQuery);
        return query;
    }

    protected virtual IQueryable<TEntity> ApplyTracking(IQueryable<TEntity> query, bool isTracking)
    {
        return isTracking ? query : query.AsNoTracking();
    }

    protected virtual IQueryable<TEntity> ApplySplitQuery(IQueryable<TEntity> query, bool isSplitQuery)
    {
        return isSplitQuery ? query.AsSplitQuery() : query;
    }
}

public interface IEntityQueryableOptions
{
    bool IsTracking { get; set; }
    bool IsSplitQuery { get; set; }
}

public class EntityQueryableOptions : IEntityQueryableOptions
{
    public bool IsTracking { get; set; } = false;
    public bool IsSplitQuery { get; set; } = false;
}