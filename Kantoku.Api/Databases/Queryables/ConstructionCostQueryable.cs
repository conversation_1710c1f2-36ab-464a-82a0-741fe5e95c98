using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class ConstructionCostQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedConstruction { get; set; } = false;
    public bool IncludedPaymentClaim { get; set; } = false;
    public bool IncludedCategorizedCosts { get; set; } = false;
}

public interface IConstructionCostQueryable
{
    IQueryable<ConstructionCost> GetConstructionCostQuery(
        ConstructionCostQueryableOptions options
    );

    IQueryable<ConstructionCost> GetConstructionCostQueryIncluded(
        ConstructionCostQueryableOptions options,
        IQueryable<ConstructionCost>? query = null

    );

    IQueryable<ConstructionCost> GetConstructionCostQueryFilter(
        ConstructionCostFilter filter,
        ConstructionCostQueryableOptions options,
        IQueryable<ConstructionCost>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class ConstructionCostQueryable(PostgreDbContext context) :
    BaseQueryable<ConstructionCost>(context), IConstructionCostQueryable
{
    public IQueryable<ConstructionCost> GetConstructionCostQuery(
        ConstructionCostQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<ConstructionCost> GetConstructionCostQueryIncluded(
        ConstructionCostQueryableOptions options,
        IQueryable<ConstructionCost>? query = null

    )
    {
        query ??= GetConstructionCostQuery(options);
        if (options.IncludedConstruction || options.IncludedAll)
        {
            query = query.Include(c => c.Construction);
        }
        if (options.IncludedPaymentClaim || options.IncludedAll)
        {
            query = query.Include(c => c.ConstructionPaymentRequest);
        }
        if (options.IncludedCategorizedCosts || options.IncludedAll)
        {
            query = query.Include(c => c.CategorizedCosts)
                .ThenInclude(c => c.Category);
        }
        return query;
    }

    public IQueryable<ConstructionCost> GetConstructionCostQueryFilter(
        ConstructionCostFilter filter,
        ConstructionCostQueryableOptions options,
        IQueryable<ConstructionCost>? query = null
    )
    {
        query ??= GetConstructionCostQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(c => c.OrgUid == filter.OrgId);
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(c => c.Construction.ProjectUid == filter.ProjectId);
        }
        if (filter.ConstructionId is not null && filter.ConstructionId != Guid.Empty)
        {
            query = query.Where(c => c.ConstructionUid == filter.ConstructionId);
        }
        if (DateOnly.TryParse(filter.DateFrom, out var startDate))
        {
            query = query.Where(c => c.StartDate >= startDate);
        }
        if (DateOnly.TryParse(filter.DateTo, out var endDate))
        {
            query = query.Where(c => c.EndDate <= endDate);
        }
        if (filter.IsPrimary is not null)
        {
            query = query.Where(c => c.Construction.IsPrimary == filter.IsPrimary);
        }

        return query;
    }
}

